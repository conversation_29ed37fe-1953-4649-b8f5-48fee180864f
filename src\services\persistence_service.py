"""
Service for persisting agent data to R2 object storage.
"""
import os
import logging
import json
import tempfile
import math # Added import for math.sqrt
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

import boto3
from botocore.exceptions import ClientError
from langchain_core.documents import Document # Ensure Document is imported

import faiss # For faiss.IndexFlatL2
from langchain_community.docstore import InMemoryDocstore # For InMemoryDocstore
from langchain.retrievers import TimeWeightedVectorStoreRetriever
from langchain_community.vectorstores import FAISS
from core.custom_agent import CustomGenerativeAgent, CustomGenerativeAgentMemory # Assuming these are correct
from core.generative_agents.memory import SafeTimeWeightedVectorStoreRetriever

from config import ENABLE_PERSISTENCE, DEFAULT_USER_ID, DEFAULT_REFLECTION_THRESHOLD, DEFAULT_MEMORY_IMPORTANCE_WEIGHT
# DATA_DIR is no longer used for primary storage if R2 is active
# file_utils are less relevant for R2, but ensure_directory_exists might be used for /tmp
from utils.file_utils import ensure_directory_exists # For /tmp
from utils.llm_utils import get_embeddings, get_llm

logger = logging.getLogger(__name__)

# --- R2/S3 Configuration ---
R2_BUCKET_NAME = os.getenv("R2_BUCKET_NAME")
R2_ACCESS_KEY_ID = os.getenv("R2_ACCESS_KEY_ID")
R2_SECRET_ACCESS_KEY = os.getenv("R2_SECRET_ACCESS_KEY")
R2_ENDPOINT_URL = os.getenv("R2_ENDPOINT_URL")

s3_client = None
if ENABLE_PERSISTENCE and R2_BUCKET_NAME and R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY and R2_ENDPOINT_URL:
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=R2_ACCESS_KEY_ID,
            aws_secret_access_key=R2_SECRET_ACCESS_KEY,
            endpoint_url=R2_ENDPOINT_URL,
        )
        logger.info(f"Service (Persistence): Successfully initialized S3 client for R2 bucket: {R2_BUCKET_NAME}")
    except Exception as e:
        logger.error(f"Service (Persistence): Failed to initialize S3 client: {e}", exc_info=True)
        s3_client = None # Ensure it's None if init fails
elif ENABLE_PERSISTENCE:
    logger.warning("Service (Persistence): Persistence is enabled, but R2/S3 environment variables are not fully set. R2 persistence will be disabled.")
    s3_client = None


# --- Agent Data Object Keys in R2 ---
def get_agent_base_key(user_id: str, canonical_agent_id: str) -> str:
    return f"users/{user_id}/agents/{canonical_agent_id}"

def get_agent_metadata_key(user_id: str, canonical_agent_id: str) -> str:
    return f"{get_agent_base_key(user_id, canonical_agent_id)}/metadata.json"

def get_agent_faiss_index_file_key(user_id: str, canonical_agent_id: str, filename: str) -> str:
    # filename will be 'index.faiss' or 'index.pkl'
    return f"{get_agent_base_key(user_id, canonical_agent_id)}/faiss_index/{filename}"


def extract_agent_metadata_for_save(agent_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract and prepare metadata from agent data for saving.
    This is similar to the original but ensures all necessary fields are present.
    """
    agent = agent_data.get("agent")
    if not agent:
        raise ValueError("Agent object missing in agent_data")
        
    # Ensure last_refreshed is a string
    last_refreshed_val = agent_data.get("last_refreshed", datetime.now())
    if isinstance(last_refreshed_val, datetime):
        last_refreshed_str = last_refreshed_val.isoformat()
    else:
        last_refreshed_str = str(last_refreshed_val) # Fallback, should ideally be isoformat

    return {
        "id": agent_data["id"], # This is the canonical_agent_id
        "user_id": agent_data.get("user_id", DEFAULT_USER_ID), # Store user_id in metadata
        "name": agent_data["name"],
        "age": agent_data.get("age"),
        "traits": agent_data.get("traits"),
        "status": agent_data.get("status"),
        "summary": agent_data.get("summary", ""),
        "last_refreshed": last_refreshed_str,
        "created_at": agent_data.get("created_at", datetime.now().isoformat()),
        "llm_model_name": getattr(agent.llm, "model_name", None) if agent.llm else None,
        "embeddings_model_name": os.getenv("DEFAULT_EMBEDDINGS_MODEL"), # Store which embeddings model was used
        "reflection_threshold": agent.memory.reflection_threshold if agent.memory else DEFAULT_REFLECTION_THRESHOLD,
        "importance_weight": agent.memory.importance_weight if agent.memory else DEFAULT_MEMORY_IMPORTANCE_WEIGHT,
        "verbose": agent.verbose if hasattr(agent, 'verbose') else False,
    }

def save_agent(agent_data: Dict[str, Any], user_id: Optional[str] = None) -> bool:
    if not ENABLE_PERSISTENCE:
        logger.debug("Service (Persistence): Persistence is disabled. Skipping save_agent.")
        return False
    if not s3_client:
        logger.error("Service (Persistence): S3 client not initialized. Cannot save agent to R2.")
        return False

    effective_user_id = user_id or agent_data.get("user_id") or DEFAULT_USER_ID
    canonical_agent_id = agent_data["id"]
    agent_instance = agent_data.get("agent")

    if not agent_instance or not hasattr(agent_instance, 'memory') or not agent_instance.memory.memory_retriever:
        logger.error(f"Service (Persistence): Agent instance or memory components missing for {canonical_agent_id}. Cannot save.")
        return False
    
    logger.info(f"Service (Persistence): Attempting to save agent ID: {canonical_agent_id} for user_id: {effective_user_id} to R2")

    try:
        # 1. Prepare and save metadata.json to R2
        # Add user_id to agent_data if not present, for extract_agent_metadata_for_save
        agent_data_for_meta = agent_data.copy()
        agent_data_for_meta["user_id"] = effective_user_id
        
        metadata_to_save = extract_agent_metadata_for_save(agent_data_for_meta)
        metadata_key = get_agent_metadata_key(effective_user_id, canonical_agent_id)
        metadata_json_string = json.dumps(metadata_to_save)
        s3_client.put_object(Bucket=R2_BUCKET_NAME, Key=metadata_key, Body=metadata_json_string.encode('utf-8'))
        logger.debug(f"Service (Persistence): Saved metadata for agent {canonical_agent_id} to R2: {metadata_key}")

        # 2. Save FAISS index (index.faiss and index.pkl) to R2
        memory_retriever = agent_instance.memory.memory_retriever
        vector_store = memory_retriever.vectorstore

        if isinstance(vector_store, FAISS):
            # FAISS saves to a directory. We need to save locally temporarily, then upload files.
            with tempfile.TemporaryDirectory() as tmpdir:
                faiss_tmp_save_path = os.path.join(tmpdir, "faiss_index_temp_save") # FAISS save_local needs a directory
                vector_store.save_local(faiss_tmp_save_path) # This creates faiss_index_temp_save.faiss and faiss_index_temp_save.pkl
                
                # Upload index.faiss
                local_faiss_file = os.path.join(faiss_tmp_save_path, "index.faiss")
                r2_faiss_key = get_agent_faiss_index_file_key(effective_user_id, canonical_agent_id, "index.faiss")
                if os.path.exists(local_faiss_file):
                    with open(local_faiss_file, "rb") as f:
                        s3_client.upload_fileobj(f, R2_BUCKET_NAME, r2_faiss_key)
                    logger.debug(f"Service (Persistence): Saved {r2_faiss_key} to R2.")
                else:
                    logger.error(f"Service (Persistence): Temporary FAISS file not found: {local_faiss_file}")
                    raise FileNotFoundError(f"Temporary FAISS file not found: {local_faiss_file}")

                # Upload index.pkl
                local_pkl_file = os.path.join(faiss_tmp_save_path, "index.pkl")
                r2_pkl_key = get_agent_faiss_index_file_key(effective_user_id, canonical_agent_id, "index.pkl")
                if os.path.exists(local_pkl_file):
                    with open(local_pkl_file, "rb") as f:
                        s3_client.upload_fileobj(f, R2_BUCKET_NAME, r2_pkl_key)
                    logger.debug(f"Service (Persistence): Saved {r2_pkl_key} to R2.")
                else:
                    logger.error(f"Service (Persistence): Temporary PKL file not found: {local_pkl_file}")
                    raise FileNotFoundError(f"Temporary PKL file not found: {local_pkl_file}")
            logger.debug(f"Service (Persistence): Saved FAISS index components for agent {canonical_agent_id} to R2.")
        else:
            logger.warning(f"Service (Persistence): Vector store for agent {canonical_agent_id} is not FAISS. Skipping FAISS save.")

        logger.info(f"Service (Persistence): Agent {canonical_agent_id} (user_id: {effective_user_id}) saved successfully to R2.")
        return True
    except ClientError as e:
        logger.error(f"Service (Persistence): S3 ClientError saving agent {canonical_agent_id} to R2: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Service (Persistence): Unexpected error saving agent {canonical_agent_id} to R2: {e}", exc_info=True)
        return False

def load_agent_metadata(user_id: str, canonical_agent_id: str) -> Optional[Dict[str, Any]]:
    logger.debug(f"Service (Persistence): Attempting to load metadata for agent ID: {canonical_agent_id}, user_id: {user_id} from R2")
    if not ENABLE_PERSISTENCE or not s3_client:
        logger.debug("Service (Persistence): Persistence or S3 client disabled. Skipping load_agent_metadata.")
        return None

    metadata_key = get_agent_metadata_key(user_id, canonical_agent_id)
    try:
        response = s3_client.get_object(Bucket=R2_BUCKET_NAME, Key=metadata_key)
        metadata_content = response['Body'].read().decode('utf-8')
        metadata = json.loads(metadata_content)
        logger.debug(f"Service (Persistence): Metadata loaded successfully from R2 for agent ID: {canonical_agent_id}.")
        return metadata
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            logger.debug(f"Service (Persistence): Metadata file not found in R2 for agent ID: {canonical_agent_id} at {metadata_key}")
        else:
            logger.error(f"Service (Persistence): S3 ClientError loading metadata from R2 for {canonical_agent_id}: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"Service (Persistence): Unexpected error loading metadata from R2 for {canonical_agent_id}: {e}", exc_info=True)
        return None

def load_agent_from_r2(user_id: str, canonical_agent_id: str) -> Optional[Dict[str, Any]]:
    logger.info(f"Service (Persistence): Attempting to load agent ID: {canonical_agent_id} from R2 for user_id: {user_id}")
    if not ENABLE_PERSISTENCE or not s3_client:
        logger.debug("Service (Persistence): Persistence or S3 client disabled. Skipping load_agent_from_r2.")
        return None

    faiss_index_instance = None
    tmp_faiss_dir = None # To store path of temporary directory for cleanup

    try:
        metadata = load_agent_metadata(user_id, canonical_agent_id)
        if not metadata:
            return None # Error already logged by load_agent_metadata

        # Prepare a temporary directory to download FAISS index files
        tmp_faiss_dir = tempfile.mkdtemp() # Creates a unique temporary directory
        faiss_load_path_prefix = os.path.join(tmp_faiss_dir, "faiss_index_load") # FAISS load_local needs a directory and prefix

        # Download index.faiss
        r2_faiss_key = get_agent_faiss_index_file_key(user_id, canonical_agent_id, "index.faiss")
        local_faiss_file = faiss_load_path_prefix + ".faiss"
        try:
            s3_client.download_file(R2_BUCKET_NAME, r2_faiss_key, local_faiss_file)
            logger.debug(f"Service (Persistence): Downloaded {r2_faiss_key} to {local_faiss_file}")
        except ClientError as e:
            # Check for 404 status code or 'NoSuchKey' more broadly
            status_code = e.response.get('ResponseMetadata', {}).get('HTTPStatusCode')
            error_code = e.response.get('Error', {}).get('Code')
            # Added a check for str(e) to catch cases where the error structure might be different but contains "404" or "Not Found"
            if status_code == 404 or error_code == 'NoSuchKey' or "Not Found" in str(e) or "404" in str(e):
                logger.warning(f"Service (Persistence): FAISS file {r2_faiss_key} not found in R2 for agent {canonical_agent_id} (Error: {str(e)}). Agent might be new or index missing. Will attempt to create an empty index.")
                # Do not raise; allow to proceed to check os.path.exists(local_faiss_file)
            else:
                logger.error(f"Service (Persistence): S3 ClientError downloading {r2_faiss_key} for agent {canonical_agent_id}: {e}", exc_info=True)
                raise # Re-raise other S3 errors
        
        # Download index.pkl
        r2_pkl_key = get_agent_faiss_index_file_key(user_id, canonical_agent_id, "index.pkl")
        local_pkl_file = faiss_load_path_prefix + ".pkl"
        try:
            s3_client.download_file(R2_BUCKET_NAME, r2_pkl_key, local_pkl_file)
            logger.debug(f"Service (Persistence): Downloaded {r2_pkl_key} to {local_pkl_file}")
        except ClientError as e:
            # Check for 404 status code or 'NoSuchKey' more broadly
            status_code = e.response.get('ResponseMetadata', {}).get('HTTPStatusCode')
            error_code = e.response.get('Error', {}).get('Code')
            # Added a check for str(e) to catch cases where the error structure might be different but contains "404" or "Not Found"
            if status_code == 404 or error_code == 'NoSuchKey' or "Not Found" in str(e) or "404" in str(e):
                logger.warning(f"Service (Persistence): PKL file {r2_pkl_key} not found in R2 for agent {canonical_agent_id} (Error: {str(e)}). Index might be incomplete. Will attempt to create an empty index.")
                # Do not raise; allow to proceed to check os.path.exists(local_pkl_file)
            else:
                logger.error(f"Service (Persistence): S3 ClientError downloading {r2_pkl_key} for agent {canonical_agent_id}: {e}", exc_info=True)
                raise

        # Load the FAISS index if both files were downloaded
        if os.path.exists(local_faiss_file) and os.path.exists(local_pkl_file):
            embeddings_model_name = metadata.get("embeddings_model_name", os.getenv("DEFAULT_EMBEDDINGS_MODEL"))
            embeddings = get_embeddings(model_name=embeddings_model_name)
            # FAISS.load_local expects the directory containing the .faiss and .pkl files, and the name (without extension)
            # Our prefix is "faiss_index_load", so it expects "faiss_index_load.faiss" and "faiss_index_load.pkl" in tmp_faiss_dir
            faiss_index_instance = FAISS.load_local(
                folder_path=tmp_faiss_dir, # Directory where files are
                index_name="faiss_index_load", # The prefix used
                embeddings=embeddings,
                allow_dangerous_deserialization=True # Be cautious with this in production
            )
            logger.debug(f"Service (Persistence): FAISS index loaded successfully for agent {canonical_agent_id}.")
        else:
            logger.warning(f"Service (Persistence): Not all FAISS components found locally for agent {canonical_agent_id}. Proceeding without loaded FAISS index.")
            faiss_index_instance = None # Explicitly set to None

        # Create the memory retriever (even if faiss_index_instance is None, agent_service might init new one)
        # If faiss_index_instance is None, TimeWeightedVectorStoreRetriever might fail or need a dummy FAISS.
        # This part needs careful handling in agent_service.py if index is missing.
        # For now, we assume agent_service.py can handle a None vector_store if index wasn't loaded.
        # Or, we could create an empty FAISS instance here if faiss_index_instance is None.
        if faiss_index_instance is None:
            logger.info(f"Service (Persistence): FAISS index not loaded for agent {canonical_agent_id}. A new one might be created by the agent service.")
            # Create an empty FAISS index if none was loaded, so agent creation doesn't fail
            # This assumes get_embeddings() is available and correct.
            embeddings_for_empty = get_embeddings(model_name=metadata.get("embeddings_model_name", os.getenv("DEFAULT_EMBEDDINGS_MODEL")))
            # FAISS requires a list of (text, embedding) tuples and a list of metadata dicts for from_embeddings
            # Or use from_texts with dummy data if that's simpler for an empty index.
            # For an empty index, it's often FAISS(embedding_function, index, docstore, index_to_docstore_id)
            # Simplest: create an empty one that can accept new docs.
            # This depends on how CustomGenerativeAgentMemory handles a retriever with no docs.
            # Let's pass None and let the agent memory constructor handle it or agent_service.
            # vector_store_for_retriever = None # Will be handled by agent_service
            # MODIFICATION: Create an empty FAISS instance if loading failed, so retriever doesn't get None.
            embeddings_for_empty = get_embeddings(model_name=metadata.get("embeddings_model_name", os.getenv("DEFAULT_EMBEDDINGS_MODEL")))
            dummy_text_to_get_dimension = " "
            embedding_vectors = embeddings_for_empty.embed_documents([dummy_text_to_get_dimension])
            if not embedding_vectors or not embedding_vectors[0]:
                logger.error("Service (Persistence): Could not determine embedding dimension for empty FAISS. Using default d=1536.")
                d = 1536 # Fallback, common for text-embedding-ada-002 or similar
            else:
                d = len(embedding_vectors[0])
            
            empty_index = faiss.IndexFlatL2(d)
            empty_docstore = InMemoryDocstore({}) # Assuming InMemoryDocstore is available

            # Define a robust relevance score function
            def robust_relevance_score_fn(score: float) -> float:
                return max(0.0, 1.0 - score / math.sqrt(2))

            vector_store_for_retriever = FAISS(
                embedding_function=embeddings_for_empty,
                index=empty_index,
                docstore=empty_docstore,
                index_to_docstore_id={},
                relevance_score_fn=robust_relevance_score_fn # Added relevance_score_fn
            )
            logger.info(f"Service (Persistence): Created new empty FAISS index with robust_relevance_score_fn for agent {canonical_agent_id} as one was not loaded from R2.")

        else: # faiss_index_instance was successfully loaded
            vector_store_for_retriever = faiss_index_instance

        # Get the LLM model
        llm_model_name = metadata.get("llm_model_name")
        llm = get_llm(model_name=llm_model_name)

        # Create the safe memory retriever
        # Using SafeTimeWeightedVectorStoreRetriever to prevent IndexError issues
        memory_retriever = SafeTimeWeightedVectorStoreRetriever(
            vectorstore=vector_store_for_retriever, # This can be None if FAISS didn't load
            decay_rate=0.01, # Example, use config
            k=int(os.getenv("MEMORY_K", "10")),
        )
        # Original warning for vector_store_for_retriever being None might be less common now,
        # as we create an empty FAISS if loading fails.
        # However, the new memory_stream population logic below also handles cases where
        # vector_store_for_retriever or its docstore might be problematic.
        if vector_store_for_retriever is None: # Keeping original log for context
             logger.warning(f"Service (Persistence): Memory retriever for {canonical_agent_id} initialized with no vector store (this should be rare now).")

        # ---- ENHANCED MEMORY_STREAM REPOPULATION WITH FALLBACK METHODS ----
        memory_stream_docs = []
        repopulation_method = "none"
        
        if vector_store_for_retriever is not None:
            # Method 1: Try to access docstore._store directly (most reliable)
            if (hasattr(vector_store_for_retriever, 'docstore') and 
                vector_store_for_retriever.docstore is not None and 
                hasattr(vector_store_for_retriever.docstore, '_store') and 
                vector_store_for_retriever.docstore._store is not None):
                
                logger.debug(f"Service (Persistence): Using Method 1 - Direct docstore._store access for agent {canonical_agent_id}")
                try:
                    docs_from_docstore = list(vector_store_for_retriever.docstore._store.values())
                    memory_stream_docs = [doc for doc in docs_from_docstore if isinstance(doc, Document)]
                    repopulation_method = "docstore_direct"
                except Exception as e:
                    logger.warning(f"Service (Persistence): Method 1 failed for agent {canonical_agent_id}: {e}")
                    memory_stream_docs = []
            
            # Method 2: Try using FAISS similarity search to extract all documents
            if not memory_stream_docs and hasattr(vector_store_for_retriever, 'index') and vector_store_for_retriever.index is not None:
                logger.debug(f"Service (Persistence): Using Method 2 - FAISS similarity search fallback for agent {canonical_agent_id}")
                try:
                    # Get the total number of vectors in the index
                    num_vectors = vector_store_for_retriever.index.ntotal
                    if num_vectors > 0:
                        # Use a generic query to retrieve all documents
                        generic_queries = ["memories", "experiences", "thoughts", "observations", "events"]
                        all_retrieved_docs = []
                        
                        for query in generic_queries:
                            try:
                                retrieved = vector_store_for_retriever.similarity_search(query, k=min(num_vectors, 100))
                                all_retrieved_docs.extend(retrieved)
                            except Exception as search_e:
                                logger.debug(f"Service (Persistence): Search with query '{query}' failed: {search_e}")
                                continue
                        
                        # Remove duplicates based on content
                        seen_content = set()
                        unique_docs = []
                        for doc in all_retrieved_docs:
                            if hasattr(doc, 'page_content') and doc.page_content not in seen_content:
                                seen_content.add(doc.page_content)
                                unique_docs.append(doc)
                        
                        memory_stream_docs = unique_docs
                        repopulation_method = "similarity_search"
                        logger.debug(f"Service (Persistence): Method 2 retrieved {len(memory_stream_docs)} unique documents")
                except Exception as e:
                    logger.warning(f"Service (Persistence): Method 2 failed for agent {canonical_agent_id}: {e}")
                    memory_stream_docs = []
            
            # Method 3: Try accessing index_to_docstore_id mapping
            if not memory_stream_docs and hasattr(vector_store_for_retriever, 'index_to_docstore_id'):
                logger.debug(f"Service (Persistence): Using Method 3 - index_to_docstore_id mapping for agent {canonical_agent_id}")
                try:
                    if (hasattr(vector_store_for_retriever, 'docstore') and 
                        vector_store_for_retriever.docstore is not None):
                        
                        for idx, doc_id in vector_store_for_retriever.index_to_docstore_id.items():
                            try:
                                doc = vector_store_for_retriever.docstore.search(doc_id)
                                if isinstance(doc, Document):
                                    memory_stream_docs.append(doc)
                            except Exception as doc_e:
                                logger.debug(f"Service (Persistence): Failed to retrieve document {doc_id}: {doc_e}")
                                continue
                        
                        repopulation_method = "index_mapping"
                        logger.debug(f"Service (Persistence): Method 3 retrieved {len(memory_stream_docs)} documents")
                except Exception as e:
                    logger.warning(f"Service (Persistence): Method 3 failed for agent {canonical_agent_id}: {e}")
                    memory_stream_docs = []
            
            # Process and sort the retrieved documents
            if memory_stream_docs:
                try:
                    # Sort by created_at timestamp if available, otherwise by buffer_idx, otherwise keep original order
                    def sort_key(doc):
                        if hasattr(doc, 'metadata') and doc.metadata:
                            # Try created_at first (most reliable)
                            if 'created_at' in doc.metadata:
                                created_at = doc.metadata['created_at']
                                if isinstance(created_at, datetime):
                                    return created_at.timestamp()
                                elif isinstance(created_at, str):
                                    try:
                                        return datetime.fromisoformat(created_at.replace('Z', '+00:00')).timestamp()
                                    except:
                                        pass
                            # Fallback to buffer_idx if available
                            if 'buffer_idx' in doc.metadata:
                                return doc.metadata.get('buffer_idx', float('inf'))
                        # Final fallback - use hash of content for consistent ordering
                        return hash(doc.page_content) if hasattr(doc, 'page_content') else 0
                    
                    sorted_documents = sorted(memory_stream_docs, key=sort_key)
                    
                    # CRITICAL FIX: Reassign buffer_idx to match the new memory_stream order
                    for i, doc in enumerate(sorted_documents):
                        if not hasattr(doc, 'metadata') or doc.metadata is None:
                            doc.metadata = {}
                        doc.metadata['buffer_idx'] = i
                    
                    # Assign the reconstructed and properly indexed memory_stream
                    memory_retriever.memory_stream = sorted_documents
                    
                    logger.info(f"Service (Persistence): Successfully reconstructed memory_stream for agent {canonical_agent_id} using method '{repopulation_method}' with {len(sorted_documents)} documents")
                    
                except Exception as e:
                    logger.error(f"Service (Persistence): Error while processing memory_stream for agent {canonical_agent_id}: {e}", exc_info=True)
                    memory_retriever.memory_stream = []
            else:
                memory_retriever.memory_stream = []
                # Enhanced diagnostic logging
                docstore_info = "Unknown"
                index_info = "Unknown"
                
                if hasattr(vector_store_for_retriever, 'docstore'):
                    docstore = vector_store_for_retriever.docstore
                    if docstore is None:
                        docstore_info = "None"
                    else:
                        docstore_info = f"Type: {type(docstore).__name__}"
                        if hasattr(docstore, '_store'):
                            if docstore._store is None:
                                docstore_info += ", _store: None"
                            else:
                                docstore_info += f", _store: {type(docstore._store).__name__} with {len(docstore._store) if hasattr(docstore._store, '__len__') else 'unknown'} items"
                        else:
                            docstore_info += ", _store: Missing"
                
                if hasattr(vector_store_for_retriever, 'index'):
                    index = vector_store_for_retriever.index
                    if index is None:
                        index_info = "None"
                    else:
                        index_info = f"Type: {type(index).__name__}"
                        if hasattr(index, 'ntotal'):
                            index_info += f", ntotal: {index.ntotal}"
                
                logger.warning(
                    f"Service (Persistence): Failed to reconstruct memory_stream for agent {canonical_agent_id}. "
                    f"Vector store type: {type(vector_store_for_retriever).__name__}, "
                    f"Docstore info: {docstore_info}, "
                    f"Index info: {index_info}. "
                    f"memory_stream initialized empty."
                )
        else:
            logger.error(
                f"Service (Persistence): vector_store_for_retriever is None for agent {canonical_agent_id}. "
                f"memory_stream initialized empty."
            )
            memory_retriever.memory_stream = []
        # ---- END OF ENHANCED MEMORY_STREAM REPOPULATION ----

        # ADDITIONAL FIX: Reset the TimeWeightedVectorStoreRetriever's internal state if possible
        # This ensures any cached buffer indices are cleared
        if hasattr(memory_retriever, '_buffer') and memory_retriever._buffer is not None:
            memory_retriever._buffer = []
            logger.debug(f"Service (Persistence): Reset TimeWeightedVectorStoreRetriever buffer for agent {canonical_agent_id}")
        
        # Reset any other internal state that might cause buffer index mismatches
        if hasattr(memory_retriever, 'buffer') and memory_retriever.buffer is not None:
            memory_retriever.buffer = []
            logger.debug(f"Service (Persistence): Reset TimeWeightedVectorStoreRetriever buffer attribute for agent {canonical_agent_id}")

        # Enhanced logging for memory_stream state
        logger.info(f"Service (Persistence): memory_stream for agent {canonical_agent_id} has {len(memory_retriever.memory_stream)} documents after repopulation logic.")
        if memory_retriever.memory_stream:
            logger.info("Service (Persistence): First up to 5 buffer_idx values in repopulated memory_stream (and their actual index):")
            for i, doc_in_stream in enumerate(memory_retriever.memory_stream[:5]):
                if hasattr(doc_in_stream, 'metadata') and 'buffer_idx' in doc_in_stream.metadata:
                    logger.info(f"  - Actual Stream Index {i}: Doc's Stored buffer_idx {doc_in_stream.metadata['buffer_idx']}, Content: {doc_in_stream.page_content[:50]}...")
                elif hasattr(doc_in_stream, 'metadata'):
                    logger.info(f"  - Actual Stream Index {i}: Doc has metadata but NO buffer_idx, Content: {doc_in_stream.page_content[:50]}...")
                else:
                    logger.info(f"  - Actual Stream Index {i}: Doc has NO metadata, Content: {doc_in_stream.page_content[:50]}...")
        elif vector_store_for_retriever and hasattr(vector_store_for_retriever, 'docstore') and \
             vector_store_for_retriever.docstore and hasattr(vector_store_for_retriever.docstore, '_store') and \
             vector_store_for_retriever.docstore._store:
            logger.info(f"Service (Persistence): memory_stream is empty, but docstore._store for agent {canonical_agent_id} contained {len(vector_store_for_retriever.docstore._store)} items (before filtering for buffer_idx).")
        else:
            logger.info(f"Service (Persistence): memory_stream is empty for agent {canonical_agent_id}, and docstore also appears empty or inaccessible.")

        memory = CustomGenerativeAgentMemory(
            llm=llm,
            memory_retriever=memory_retriever,
            reflection_threshold=metadata.get("reflection_threshold", DEFAULT_REFLECTION_THRESHOLD),
            importance_weight=metadata.get("importance_weight", DEFAULT_MEMORY_IMPORTANCE_WEIGHT),
            verbose=metadata.get("verbose", False),
        )

        # Create the agent
        agent = CustomGenerativeAgent(
            name=metadata["name"],
            age=metadata.get("age"),
            traits=metadata.get("traits", "N/A"),
            status=metadata.get("status", ""),
            memory=memory,
            llm=llm,
            verbose=metadata.get("verbose", False),
        )

        agent_data_loaded = {
            "id": canonical_agent_id, # from metadata or input
            "user_id": user_id, # from input
            "name": metadata["name"],
            "age": metadata.get("age"),
            "traits": metadata.get("traits", "N/A"),
            "status": metadata.get("status", ""),
            "agent": agent, # The fully rehydrated agent object
            "summary": metadata.get("summary", ""),
            "last_refreshed": metadata.get("last_refreshed", datetime.now().isoformat()),
            "created_at": metadata.get("created_at", datetime.now().isoformat())
        }

        logger.info(f"Service (Persistence): Agent {canonical_agent_id} (user_id: {user_id}) constructed successfully using data from R2.")
        return agent_data_loaded

    except Exception as e:
        logger.error(f"Service (Persistence): Error loading agent {canonical_agent_id} from R2: {e}", exc_info=True)
        return None
    finally:
        if tmp_faiss_dir and os.path.exists(tmp_faiss_dir):
            try:
                # Clean up the temporary directory and its contents
                for root, dirs, files in os.walk(tmp_faiss_dir, topdown=False):
                    for name in files:
                        os.remove(os.path.join(root, name))
                    for name in dirs:
                        os.rmdir(os.path.join(root, name))
                os.rmdir(tmp_faiss_dir)
                logger.debug(f"Service (Persistence): Cleaned up temporary FAISS directory: {tmp_faiss_dir}")
            except Exception as e_cleanup:
                logger.error(f"Service (Persistence): Error cleaning up temporary FAISS directory {tmp_faiss_dir}: {e_cleanup}", exc_info=True)


def list_persisted_agents(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    effective_user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Service (Persistence): Listing persisted agents from R2 for user_id: {effective_user_id}")
    if not ENABLE_PERSISTENCE or not s3_client:
        logger.debug("Service (Persistence): Persistence or S3 client disabled. Skipping list_persisted_agents.")
        return []

    agents_metadata_list = []
    user_agents_prefix = f"users/{effective_user_id}/agents/"
    
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(Bucket=R2_BUCKET_NAME, Prefix=user_agents_prefix, Delimiter='/')

        for page in page_iterator:
            for common_prefix in page.get('CommonPrefixes', []):
                # common_prefix['Prefix'] is like 'users/default/agents/agent_id_123/'
                agent_id_from_key = common_prefix['Prefix'].split('/')[-2] # Get the agent_id part
                if agent_id_from_key:
                    metadata = load_agent_metadata(effective_user_id, agent_id_from_key)
                    if metadata:
                        agents_metadata_list.append(metadata)
        
        logger.info(f"Service (Persistence): Listed {len(agents_metadata_list)} persisted agents from R2 for user_id: {effective_user_id}.")
        return agents_metadata_list
    except ClientError as e:
        logger.error(f"Service (Persistence): S3 ClientError listing agents from R2 for user {effective_user_id}: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"Service (Persistence): Unexpected error listing agents from R2 for user {effective_user_id}: {e}", exc_info=True)
        return []


def delete_persisted_agent(user_id: str, canonical_agent_id: str) -> bool:
    logger.info(f"Service (Persistence): Attempting to delete persisted agent ID: {canonical_agent_id} from R2 for user_id: {user_id}")
    if not ENABLE_PERSISTENCE or not s3_client:
        logger.debug("Service (Persistence): Persistence or S3 client disabled. Skipping delete_persisted_agent.")
        return False

    objects_to_delete = []
    base_key = get_agent_base_key(user_id, canonical_agent_id)
    
    # List all objects under the agent's base key
    try:
        response = s3_client.list_objects_v2(Bucket=R2_BUCKET_NAME, Prefix=base_key + "/")
        if 'Contents' in response:
            for obj in response['Contents']:
                objects_to_delete.append({'Key': obj['Key']})
        
        # Also add the metadata key if it wasn't listed (e.g. if no trailing slash in prefix)
        # metadata_key = get_agent_metadata_key(user_id, canonical_agent_id)
        # if not any(obj['Key'] == metadata_key for obj in objects_to_delete):
        #     objects_to_delete.append({'Key': metadata_key}) # This logic might be redundant if prefix includes trailing slash

        if not objects_to_delete:
            logger.warning(f"Service (Persistence): No objects found in R2 for agent ID: {canonical_agent_id}, user_id: {user_id} under prefix {base_key}/. Nothing to delete.")
            return True # Or False if we consider "not found" an issue

        delete_request = {'Objects': objects_to_delete}
        s3_client.delete_objects(Bucket=R2_BUCKET_NAME, Delete=delete_request)
        logger.info(f"Service (Persistence): Successfully deleted {len(objects_to_delete)} objects from R2 for agent ID: {canonical_agent_id}, user_id: {user_id}.")
        return True
    except ClientError as e:
        logger.error(f"Service (Persistence): S3 ClientError deleting agent {canonical_agent_id} from R2: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Service (Persistence): Unexpected error deleting agent {canonical_agent_id} from R2: {e}", exc_info=True)
        return False

# initialize_data_directory is no longer relevant for R2 as bucket/prefix structure is implicit.
# It could be used to ensure /tmp exists if needed, but /tmp is standard.
def initialize_data_directory() -> None:
    logger.info("Service (Persistence): initialize_data_directory called. For R2, this mainly ensures /tmp is usable if needed.")
    if ENABLE_PERSISTENCE: # Only if persistence (which now means R2) is on
        try:
            ensure_directory_exists("/tmp") # Ensure /tmp exists and is writable, though usually it is.
            logger.debug("Service (Persistence): Ensured /tmp directory exists.")
        except Exception as e:
            logger.error(f"Service (Persistence): Could not ensure /tmp directory: {e}", exc_info=True)


# get_all_user_ids would also need to be adapted to list prefixes in R2 under 'users/'
def get_all_user_ids() -> List[str]:
    logger.debug("Service (Persistence): Attempting to get all user IDs from R2.")
    if not ENABLE_PERSISTENCE or not s3_client:
        logger.debug("Service (Persistence): Persistence or S3 client disabled. Returning default user ID.")
        return [DEFAULT_USER_ID]

    user_ids = set() # Use a set to avoid duplicates if keys are structured oddly
    users_base_prefix = "users/"
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(Bucket=R2_BUCKET_NAME, Prefix=users_base_prefix, Delimiter='/')

        for page in page_iterator:
            for common_prefix in page.get('CommonPrefixes', []):
                # common_prefix['Prefix'] is like 'users/user_id_123/'
                user_id_from_key = common_prefix['Prefix'].replace(users_base_prefix, "").strip('/')
                if user_id_from_key:
                    user_ids.add(user_id_from_key)
        
        if not user_ids: # If no users found, perhaps return default or empty list
            logger.debug(f"Service (Persistence): No user directories found in R2 under {users_base_prefix}. Returning default.")
            return [DEFAULT_USER_ID]

        logger.debug(f"Service (Persistence): Found user IDs from R2: {list(user_ids)}")
        return list(user_ids)
    except ClientError as e:
        logger.error(f"Service (Persistence): S3 ClientError getting user IDs from R2: {e}", exc_info=True)
        return [DEFAULT_USER_ID] # Fallback
    except Exception as e:
        logger.error(f"Service (Persistence): Unexpected error getting user IDs from R2: {e}", exc_info=True)
        return [DEFAULT_USER_ID] # Fallback
