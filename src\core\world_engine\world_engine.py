"""
World Engine implementation for managing turn-based game mechanics.

This module provides the core functionality for:
- Managing world rules using a vector store
- Executing turns with observation generation
- Evaluating action feasibility based on world rules
- Performing weighted dice rolls for success/failure
- Analyzing failures and updating agent states
"""

import logging
import random
import math
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

import faiss
from langchain.chains import LL<PERSON>hain
from langchain_community.vectorstores import FAISS
from langchain_community.docstore import InMemoryDocstore
from langchain_core.language_models import BaseLanguageModel
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_text_splitters import RecursiveCharacterTextSplitter

from utils.llm_utils import get_llm, get_embeddings
from utils.date_utils import parse_datetime
from core.generative_agents.memory import SafeTimeWeightedVectorStoreRetriever
from config import DEFAULT_MEMORY_K

logger = logging.getLogger(__name__)


class WorldEngine:
    """
    Manages world rules and turn-based game mechanics for agent simulations.

    The WorldEngine is responsible for:
    - Storing and retrieving world rules via vector store
    - Generating observations for agents based on world state
    - Evaluating action feasibility using LLM and world rules
    - Managing turn execution flow
    """

    def __init__(
        self,
        world_id: str,
        llm: Optional[BaseLanguageModel] = None,
        verbose: bool = False
    ):
        """
        Initialize the WorldEngine.

        Args:
            world_id: Unique identifier for this world instance
            llm: Language model to use (defaults to system default)
            verbose: Enable verbose logging
        """
        self.world_id = world_id
        self.llm = llm or get_llm()
        self.verbose = verbose
        self.world_rules_store = self._initialize_vector_store()
        self.turn_count = 0
        self.world_state = {}  # Can store additional world state here

        logger.info(f"WorldEngine initialized for world_id: {world_id}")

    def _initialize_vector_store(self) -> FAISS:
        """Initialize an empty FAISS vector store for world rules."""
        embeddings = get_embeddings()

        # Get embedding dimensions
        dummy_text = " "
        embedding_vectors = embeddings.embed_documents([dummy_text])
        if not embedding_vectors or not embedding_vectors[0]:
            raise ValueError("Could not determine embedding dimension")
        d = len(embedding_vectors[0])

        # Create FAISS index
        index = faiss.IndexFlatL2(d)
        docstore = InMemoryDocstore({})
        index_to_docstore_id = {}

        def relevance_score_fn(score: float) -> float:
            return max(0.0, 1.0 - score / math.sqrt(2))

        vector_store = FAISS(
            embedding_function=embeddings,
            index=index,
            docstore=docstore,
            index_to_docstore_id=index_to_docstore_id,
            relevance_score_fn=relevance_score_fn
        )

        return vector_store

    def add_world_rules(self, rules: List[str]) -> None:
        """
        Add world rules to the vector store.

        Args:
            rules: List of world rule strings
        """
        if not rules:
            return

        logger.info(f"Adding {len(rules)} world rules to world {self.world_id}")

        # Add rules to vector store
        texts = [f"World Rule: {rule}" for rule in rules]
        self.world_rules_store.add_texts(texts)

        logger.debug(f"World rules added successfully")

    def get_relevant_world_rules(self, query: str, k: int = 5) -> List[str]:
        """
        Retrieve world rules relevant to a query.

        Args:
            query: Query string to search for relevant rules
            k: Number of rules to retrieve

        Returns:
            List of relevant world rules
        """
        try:
            docs = self.world_rules_store.similarity_search(query, k=k)
            rules = [doc.page_content.replace("World Rule: ", "") for doc in docs]
            return rules
        except Exception as e:
            logger.warning(f"Error retrieving world rules: {e}")
            return []

    def construct_observations(self, agents_data: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Construct observations for each agent based on their status and world state.
        Uses a single batched LLM call to generate observations for all agents at once.

        Args:
            agents_data: List of agent data dictionaries

        Returns:
            Dictionary mapping agent_id to observation string
        """
        observations = {}

        # Create a summary of all agent statuses and locations for context
        agent_statuses = []
        agent_info = {}  # Map to store agent_id -> agent_name for later use

        for agent_data in agents_data:
            agent_id = agent_data["id"]
            agent_name = agent_data["name"]
            agent_status = agent_data["status"]
            agent_location = agent_data.get("location", "Unknown")
            agent_statuses.append(f"{agent_name} (at {agent_location}): {agent_status}")
            agent_info[agent_id] = agent_name

        world_context = "\n".join(agent_statuses)

        # If there's only one agent, use the original approach to avoid unnecessary complexity
        if len(agents_data) <= 1:
            for agent_data in agents_data:
                agent_id = agent_data["id"]
                agent_name = agent_data["name"]

                # Use LLM to generate contextual observation
                agent_location = agent_data.get("location", "Unknown")
                prompt = PromptTemplate.from_template(
                    """Given the current world state and agent statuses, generate an observation for {agent_name}.

World State:
{world_context}

Current Turn: {turn_count}

{agent_name} is currently at: {agent_location}

Generate a specific observation that {agent_name} would perceive in this situation.
The observation should be relevant to their current status, location, and the world around them.
Consider what they might see, hear, or sense from their current location and how it relates to other agents' locations and activities.

Observation for {agent_name}:"""
                )

                chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)
                observation = chain.run(
                    agent_name=agent_name,
                    agent_location=agent_location,
                    world_context=world_context,
                    turn_count=self.turn_count
                ).strip()

                observations[agent_id] = f"You observe: {observation}"

            return observations

        # For multiple agents, use a batched approach with JSON output
        try:
            # Create a list of agent names for the prompt
            agent_names = [agent_data["name"] for agent_data in agents_data]
            agent_ids = [agent_data["id"] for agent_data in agents_data]

            # Create a parser for JSON output
            parser = JsonOutputParser()

            # Create a prompt that instructs the LLM to generate observations for all agents
            batch_prompt = PromptTemplate.from_template(
                """Given the current world state and agent statuses, generate observations for all agents.

World State:
{world_context}

Current Turn: {turn_count}

Agents: {agent_names}

For each agent, generate a specific observation that they would perceive in this situation.
The observation should be relevant to their current status, location, and the world around them.
Consider what they might see, hear, or sense from their current location and how it relates to other agents' locations and activities.

{format_instructions}

Return a JSON object where the keys are the agent IDs and the values are the observations.
The agent IDs are: {agent_ids}

Example format:
{{
    "agent-id-1": "Observation for first agent",
    "agent-id-2": "Observation for second agent"
}}

Observations:"""
            )

            # Create a JSON-optimized LLM for the batched approach
            # Using response_format={"type": "json_object"} instructs the model at the API level
            # to return valid JSON, which is more reliable than just using the JsonOutputParser alone
            from utils.llm_utils import get_llm
            json_llm = get_llm(
                model_name=None,  # Use the default model
                temperature=0.7,  # Keep the same temperature as the main LLM
                response_format={"type": "json_object"}  # Force JSON output at the API level
            )

            # Create a chain that combines the prompt, JSON-optimized LLM, and parser
            chain = batch_prompt | json_llm | parser

            # Invoke the chain with the input variables
            batch_result = chain.invoke({
                "world_context": world_context,
                "turn_count": self.turn_count,
                "agent_names": agent_names,
                "agent_ids": agent_ids,
                "format_instructions": parser.get_format_instructions()
            })

            # Process the results
            for agent_id, observation in batch_result.items():
                observations[agent_id] = f"You observe: {observation}"

            # Check if any agent is missing from the results and generate individual observations if needed
            for agent_id in agent_ids:
                if agent_id not in observations:
                    logger.warning(f"Agent {agent_id} missing from batch observation results. Generating individually.")
                    agent_name = agent_info[agent_id]

                    # Fallback to individual generation
                    # Get agent location for the fallback
                    agent_location = "Unknown"
                    for agent_data in agents_data:
                        if agent_data["id"] == agent_id:
                            agent_location = agent_data.get("location", "Unknown")
                            break

                    prompt = PromptTemplate.from_template(
                        """Given the current world state and agent statuses, generate an observation for {agent_name}.

World State:
{world_context}

Current Turn: {turn_count}

{agent_name} is currently at: {agent_location}

Generate a specific observation that {agent_name} would perceive in this situation.
The observation should be relevant to their current status, location, and the world around them.
Consider what they might see, hear, or sense from their current location and how it relates to other agents' locations and activities.

Observation for {agent_name}:"""
                    )

                    chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)
                    observation = chain.run(
                        agent_name=agent_name,
                        agent_location=agent_location,
                        world_context=world_context,
                        turn_count=self.turn_count
                    ).strip()

                    observations[agent_id] = f"You observe: {observation}"

        except Exception as e:
            # If the batched approach fails, fall back to the original approach
            logger.error(f"Error in batched observation generation: {e}. Falling back to individual generation.")

            for agent_data in agents_data:
                agent_id = agent_data["id"]
                agent_name = agent_data["name"]
                agent_location = agent_data.get("location", "Unknown")

                # Use LLM to generate contextual observation
                prompt = PromptTemplate.from_template(
                    """Given the current world state and agent statuses, generate an observation for {agent_name}.

World State:
{world_context}

Current Turn: {turn_count}

{agent_name} is currently at: {agent_location}

Generate a specific observation that {agent_name} would perceive in this situation.
The observation should be relevant to their current status, location, and the world around them.
Consider what they might see, hear, or sense from their current location and how it relates to other agents' locations and activities.

Observation for {agent_name}:"""
                )

                chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)
                observation = chain.run(
                    agent_name=agent_name,
                    agent_location=agent_location,
                    world_context=world_context,
                    turn_count=self.turn_count
                ).strip()

                observations[agent_id] = f"You observe: {observation}"

        return observations

    def evaluate_action_feasibility(
        self,
        agent_id: str,
        agent_name: str,
        action: str,
        agent_status: str,
        agent_location: str,
        observation: str,
        relevant_memories: List[str]
    ) -> bool:
        """
        Evaluate the feasibility of an action based on world rules.

        Args:
            agent_id: ID of the agent
            agent_name: Name of the agent
            action: The intended action
            agent_status: Current status of the agent
            agent_location: Current location of the agent
            observation: The observation that prompted the action
            relevant_memories: Recent relevant memories of the agent

        Returns:
            True if action is possible, False if impossible
        """
        # Get relevant world rules for this action
        relevant_rules = self.get_relevant_world_rules(action, k=5)
        rules_context = "\n".join([f"- {rule}" for rule in relevant_rules]) if relevant_rules else "No specific rules apply"

        # Format memories
        memories_context = "\n".join([f"- {memory}" for memory in relevant_memories[-5:]]) if relevant_memories else "No recent memories"

        prompt = PromptTemplate.from_template(
            """Evaluate whether the following action is possible or impossible based on world rules and context.

Agent: {agent_name}
Current Status: {agent_status}
Location: {agent_location}
Observation: {observation}
Intended Action: {action}

Recent Memories:
{memories_context}

Relevant World Rules:
{rules_context}

Consider the agent's current state, location, the world rules, and the context.
Determine if this action is POSSIBLE or IMPOSSIBLE.

An action is IMPOSSIBLE if:
- It violates fundamental world rules
- The agent lacks necessary physical capabilities
- Required objects/people are not accessible from their location
- Environmental constraints prevent the action

An action is POSSIBLE if:
- It follows world rules
- The agent has the necessary capabilities
- Required resources are accessible
- No fundamental constraints prevent it

Respond with only one word: POSSIBLE or IMPOSSIBLE

Feasibility:"""
        )

        chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)

        try:
            result = chain.run(
                agent_name=agent_name,
                agent_status=agent_status,
                agent_location=agent_location,
                observation=observation,
                action=action,
                memories_context=memories_context,
                rules_context=rules_context
            ).strip()

            # Parse POSSIBLE/IMPOSSIBLE response
            is_possible = "POSSIBLE" in result.upper()

            logger.debug(f"Action feasibility for {agent_name}: {action} -> {'POSSIBLE' if is_possible else 'IMPOSSIBLE'}")
            return is_possible

        except Exception as e:
            logger.error(f"Error evaluating action feasibility: {e}")
            return True  # Default to possible



    def analyze_failure(
        self,
        agent_name: str,
        action: str,
        observation: str,
        agent_status: str,
        relevant_memories: List[str]
    ) -> str:
        """
        Analyze why an action failed and generate explanation.

        Args:
            agent_name: Name of the agent
            action: The failed action
            observation: The observation that prompted the action
            agent_status: Current status of the agent
            relevant_memories: Recent relevant memories

        Returns:
            Explanation of the failure
        """
        # Get relevant world rules
        relevant_rules = self.get_relevant_world_rules(action, k=3)
        rules_context = "\n".join([f"- {rule}" for rule in relevant_rules]) if relevant_rules else "No specific rules"

        memories_context = "\n".join([f"- {memory}" for memory in relevant_memories[-3:]]) if relevant_memories else "No recent memories"

        prompt = PromptTemplate.from_template(
            """Analyze why the following action failed.

Agent: {agent_name}
Status: {agent_status}
Observation: {observation}
Attempted Action: {action}

Recent Memories:
{memories_context}

World Rules:
{rules_context}

Identify the most likely cause for the action's failure. Be specific and concise.
Focus on physical limitations, world rules, or situational constraints.

Failure Reason:"""
        )

        chain = LLMChain(llm=self.llm, prompt=prompt, verbose=self.verbose)

        failure_reason = chain.run(
            agent_name=agent_name,
            agent_status=agent_status,
            observation=observation,
            action=action,
            memories_context=memories_context,
            rules_context=rules_context
        ).strip()

        return failure_reason

    def update_agent_status_and_location(
        self,
        agent_name: str,
        current_status: str,
        current_location: str,
        recent_memory: str,
        action_result: str
    ) -> Dict[str, str]:
        """
        Generate updated status and location for an agent based on recent events.

        Args:
            agent_name: Name of the agent
            current_status: Current status string
            current_location: Current location string
            recent_memory: Most recent memory/event
            action_result: Result of the agent's action (success/failure description)

        Returns:
            Dictionary with updated status and location
        """
        prompt = PromptTemplate.from_template(
            """Update the agent's status and location based on recent events.

Agent: {agent_name}
Current Status: {current_status}
Current Location: {current_location}
Recent Event: {recent_memory}
Action Result: {action_result}

Generate updated status and location that reflect the agent's current state after these events.
Consider if the action might have changed their location or if they should move based on the events.

Return your response as a JSON object with exactly this format:
{{
    "status": "Updated status describing their physical/mental state and immediate situation (1-2 sentences)",
    "location": "Updated location where the agent is now"
}}

Response:"""
        )

        # Create a JSON-optimized LLM for reliable JSON output
        # Using response_format={"type": "json_object"} instructs the model at the API level
        # to return valid JSON, which is more reliable than just parsing text output
        from utils.llm_utils import get_llm
        json_llm = get_llm(
            model_name=None,  # Use the default model
            temperature=0.7,  # Keep the same temperature as the main LLM
            response_format={"type": "json_object"}  # Force JSON output at the API level
        )

        chain = LLMChain(llm=json_llm, prompt=prompt, verbose=self.verbose)

        try:
            result = chain.run(
                agent_name=agent_name,
                current_status=current_status,
                current_location=current_location,
                recent_memory=recent_memory,
                action_result=action_result
            ).strip()

            # Parse JSON response
            import json
            parsed_result = json.loads(result)

            return {
                "status": parsed_result.get("status", current_status),
                "location": parsed_result.get("location", current_location)
            }

        except Exception as e:
            logger.error(f"Error updating agent status and location: {e}")
            # Return current values as fallback
            return {
                "status": current_status,
                "location": current_location
            }

    def execute_turn(self, agents_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Execute a complete turn in the world simulation.

        Args:
            agents_data: List of agent data dictionaries with 'agent' objects

        Returns:
            Dictionary containing turn execution results
        """
        self.turn_count += 1
        logger.info(f"Executing turn {self.turn_count} for world {self.world_id}")

        turn_results = {
            "turn": self.turn_count,
            "world_id": self.world_id,
            "timestamp": datetime.now().isoformat(),
            "agent_results": {}
        }

        # Step 1: Construct observations for all agents
        observations = self.construct_observations(agents_data)

        # Step 2: Process each agent's turn
        for agent_data in agents_data:
            agent_id = agent_data["id"]
            agent_name = agent_data["name"]
            agent = agent_data["agent"]  # The actual GenerativeAgent instance

            agent_result = {
                "observation": observations[agent_id],
                "action": None,
                "success": None,
                "failure_reason": None,
                "updated_status": None
            }

            try:
                # Step 3: Agent reacts to observation
                is_dialogue, reaction = agent.generate_reaction(
                    observation=observations[agent_id],
                    now=datetime.now()
                )

                agent_result["action"] = reaction

                # Get agent's recent memories for context
                recent_memories = []
                if hasattr(agent.memory, 'memory_stream'):
                    recent_memories = [doc.page_content for doc in agent.memory.memory_stream[-5:]]

                # Step 4: Evaluate action feasibility
                agent_location = agent_data.get("location", "Unknown")
                is_possible = self.evaluate_action_feasibility(
                    agent_id=agent_id,
                    agent_name=agent_name,
                    action=reaction,
                    agent_status=agent.status,
                    agent_location=agent_location,
                    observation=observations[agent_id],
                    relevant_memories=recent_memories
                )

                # Step 5: Determine success (action succeeds if possible)
                success = is_possible
                agent_result["success"] = success
                agent_result["is_possible"] = is_possible

                # Step 6: Handle failure
                if not success:
                    failure_reason = self.analyze_failure(
                        agent_name=agent_name,
                        action=reaction,
                        observation=observations[agent_id],
                        agent_status=agent.status,
                        relevant_memories=recent_memories
                    )

                    agent_result["failure_reason"] = failure_reason

                    # Add failure to agent's memory and trigger reflection
                    failure_memory = f"I attempted to {reaction} but failed. {failure_reason}"
                    agent.add_memory(failure_memory)

                    # Check if reflection should be triggered
                    if hasattr(agent.memory, 'reflect'):
                        agent.memory.reflect()

                # Step 7: Update agent status and location
                action_result = f"Successfully: {reaction}" if success else f"Failed: {failure_reason}"
                current_location = agent_data.get("location", "Unknown")
                updates = self.update_agent_status_and_location(
                    agent_name=agent_name,
                    current_status=agent.status,
                    current_location=current_location,
                    recent_memory=reaction,
                    action_result=action_result
                )

                agent.status = updates["status"]
                agent.location = updates["location"]
                agent_data["status"] = updates["status"]
                agent_data["location"] = updates["location"]
                agent_result["updated_status"] = updates["status"]
                agent_result["updated_location"] = updates["location"]

            except Exception as e:
                logger.error(f"Error processing turn for agent {agent_id}: {e}")
                agent_result["error"] = str(e)

            turn_results["agent_results"][agent_id] = agent_result

        logger.info(f"Turn {self.turn_count} completed for world {self.world_id}")
        return turn_results
