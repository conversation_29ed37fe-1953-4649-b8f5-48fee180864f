import os
import logging
import colorlog # Added colorlog
from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError # Added
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
from src.middleware import TimeoutMiddleware, RequestLoggingMiddleware

import src.routes.agent_routes as agent_routes
import src.routes.memory_routes as memory_routes
import src.routes.github_config_routes as github_config_routes
import src.routes.world_engine_routes as world_engine_routes
from langchain.globals import set_debug # Added for Langchain internal debugging
from src.services.persistence_service import initialize_data_directory
from src.services.startup_service import preload_agents
from src.config import ENABLE_PERSISTENCE

# Configure logging
log_level_str = os.environ.get("LOG_LEVEL", "INFO").upper()
numeric_log_level = getattr(logging, log_level_str, logging.INFO)

# Configure the root logger
root_logger = logging.getLogger() # Get the root logger
root_logger.setLevel(numeric_log_level)

if root_logger.hasHandlers():
    root_logger.handlers.clear()

handler = colorlog.StreamHandler() # Default stream is sys.stderr
formatter = colorlog.ColoredFormatter(
    "%(log_color)s%(levelname)-8s%(reset)s %(asctime)s %(name)s:%(funcName)s %(blue)s%(message)s",
    datefmt='%Y-%m-%d %H:%M:%S',
    reset=True,
    log_colors={
        'DEBUG':    'cyan',
        'INFO':     'green',
        'WARNING':  'yellow',
        'ERROR':    'red',
        'CRITICAL': 'red,bg_white',
    },
    secondary_log_colors={},
    style='%'
)
handler.setFormatter(formatter)
root_logger.addHandler(handler)

# Get a logger for the application itself, it will use root's config
app_specific_logger = logging.getLogger(__name__)
# Ensure propagation is on if using child loggers elsewhere and want them to use this root config
# app_specific_logger.propagate = True # Default is True for non-root loggers, root logger propagation is not a concept

# Configure Langchain internal debugging
langchain_debug_env = os.environ.get("LANGCHAIN_DEBUG_LOGGING", "False").lower()
if langchain_debug_env in ["true", "1"]:
    app_specific_logger.info("LANGCHAIN_DEBUG_LOGGING enabled via environment variable.")
    set_debug(True) # This sets 'langchain' logger level to DEBUG and might add a handler

    # Ensure Langchain logs use our root handler and formatting
    langchain_logger = logging.getLogger("langchain")
    langchain_logger.setLevel(logging.DEBUG) # Ensure level is DEBUG
    if langchain_logger.hasHandlers():
        langchain_logger.handlers.clear() # Remove handlers added by set_debug
    langchain_logger.propagate = True # Ensure it propagates to root
else:
    # If Langchain debugging is off, ensure its level is something higher (e.g., INFO or WARNING)
    # and clear handlers if any, then call set_debug(False)
    # app_specific_logger.info("LANGCHAIN_DEBUG_LOGGING disabled or not set.") # Optional: log if disabled
    langchain_logger = logging.getLogger("langchain")
    # Set to a higher level, e.g. WARNING, if not in debug mode.
    langchain_logger.setLevel(logging.WARNING)
    if langchain_logger.hasHandlers():
        langchain_logger.handlers.clear()
    langchain_logger.propagate = True
    set_debug(False) # Call this to let Langchain do its own internal disabling if any


# Create FastAPI application
app = FastAPI(
    title="Generative Agents API",
    description="API for managing and interacting with LangChain Generative Agents",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add timeout middleware to prevent worker timeouts
timeout_seconds = int(os.environ.get("REQUEST_TIMEOUT", "240"))  # 4 minutes
warning_threshold = int(os.environ.get("REQUEST_WARNING_THRESHOLD", "60"))  # 1 minute
app.add_middleware(
    TimeoutMiddleware,
    timeout_seconds=timeout_seconds,
    warning_threshold=warning_threshold,
    log_slow_requests=True
)

# Add request logging middleware for debugging (only in debug mode)
if os.environ.get("DEBUG", "False").lower() in ("true", "1"):
    app.add_middleware(
        RequestLoggingMiddleware,
        log_body=False  # Set to True if you need to debug request bodies
    )

# Initialize data directory if persistence is enabled
if ENABLE_PERSISTENCE:
    initialize_data_directory()
    app_specific_logger.info("Initialized data directory for persistence") # Changed logger to app_specific_logger

    # Preload agents from disk
    preload_agents()

# Include routes
app.include_router(agent_routes.router, prefix="/api/agents", tags=["Agents"])
app.include_router(memory_routes.router, prefix="/api/memories", tags=["Memories"])
app.include_router(github_config_routes.router, prefix="/api/v1/configs", tags=["GitHub Configurations"])
app.include_router(world_engine_routes.router, prefix="/api/world-engine", tags=["World Engine"])

# Custom OpenAPI documentation
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
    )

@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    return get_redoc_html(
        openapi_url="/openapi.json",
        title=app.title + " - ReDoc",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
    )

@app.get("/openapi.json", include_in_schema=False)
async def get_open_api_endpoint():
    return JSONResponse(get_openapi(title=app.title, version=app.version, routes=app.routes))

@app.get("/")
async def root():
    return {
        "message": "Welcome to the Generative Agents API",
        "documentation": "/docs",
        "version": app.version
    }

@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring and load balancers.
    """
    import psutil
    import time

    try:
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()

        return {
            "status": "healthy",
            "timestamp": time.time(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available / (1024 * 1024)
            },
            "configuration": {
                "timeout_seconds": int(os.environ.get("REQUEST_TIMEOUT", "240")),
                "warning_threshold": int(os.environ.get("REQUEST_WARNING_THRESHOLD", "60")),
                "gunicorn_timeout": int(os.environ.get("GUNICORN_TIMEOUT", "300")),
                "workers": int(os.environ.get("GUNICORN_WORKERS", "2"))
            }
        }
    except Exception as e:
        app_specific_logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": time.time(),
            "error": str(e)
        }

# Error handling
@app.exception_handler(RequestValidationError)
async def http_validation_exception_handler(request: Request, exc: RequestValidationError):
    app_specific_logger.warning(f"HTTP 422 Request Validation Error: {exc.errors()} for request: {request.method} {request.url}")
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()},
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    app_specific_logger.error(f"Unhandled exception: {exc} for request: {request.method} {request.url}", exc_info=True) # Changed logger to app_specific_logger
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "message": str(exc)},
    )
